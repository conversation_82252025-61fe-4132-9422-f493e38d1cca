import { useState, useEffect } from 'react'
import Versions from './components/Versions'
import TradingSignal from './components/TradingSignal'

interface TradeSignal {
  type: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  timestamp: number
  price: number
  suggestedDuration?: number
  reason: string
}

function App(): React.JSX.Element {
  const [currentSignal, setCurrentSignal] = useState<TradeSignal | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [nextCandleTime, setNextCandleTime] = useState<number | null>(null)
  const [selectedAsset, setSelectedAsset] = useState<string>('AUDUSD_otc')
  const [isBotRunning, setIsBotRunning] = useState(false)

  useEffect(() => {
    // Listen for WebSocket events
    const unsubscribeSignal = window.api.on('ws:event', (...args: unknown[]) => {
      const [event, data] = args as [string, unknown]
      if (event === 'trade:signal') {
        setCurrentSignal(data as TradeSignal)
        setIsLoading(false)
      } else if (event === 'trade:candle-close') {
        const candleData = data as { nextCloseTime: number }
        setNextCandleTime(candleData.nextCloseTime)
      } else if (event === 'trade:generating-signal') {
        setIsLoading(true)
      }
    })

    return () => {
      unsubscribeSignal()
    }
  }, [])

  const handleSelectAsset = (): void => {
    setIsLoading(true)
    window.api.invoke('ws:selectAsset', selectedAsset)
  }

  const handleStartBot = (): void => {
    setIsLoading(true)
    setIsBotRunning(true)
    window.api.invoke('bot:start', selectedAsset)
  }

  const handleStopBot = (): void => {
    setIsBotRunning(false)
    setCurrentSignal(null)
    setNextCandleTime(null)
    window.api.invoke('bot:stop')
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto py-8">
        <h1 className="text-4xl font-bold text-center mb-8 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          Trading Signal Bot
        </h1>

        {/* Control Panel */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="bg-gray-900 rounded-xl p-6 shadow-xl">
            <div className="flex flex-col gap-4">
              {/* Asset Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  Select Trading Pair
                </label>
                <select
                  value={selectedAsset}
                  onChange={(e) => setSelectedAsset(e.target.value)}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-purple-500 focus:outline-none transition-colors"
                  disabled={isBotRunning}
                >
                  <option value="AUDUSD_otc">AUD/USD OTC</option>
                  <option value="BTCUSD">Bitcoin</option>
                  <option value="GBPUSD_otc">GBP/USD OTC</option>
                  <option value="USDJPY_otc">USD/JPY OTC</option>
                </select>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4">
                <button
                  className="flex-1 px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                  onClick={handleSelectAsset}
                  disabled={isBotRunning || isLoading}
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    boxShadow: '0 4px 15px 0 rgba(102, 126, 234, 0.4)'
                  }}
                >
                  Select Asset
                </button>

                {!isBotRunning ? (
                  <button
                    className="flex-1 px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                    onClick={handleStartBot}
                    disabled={isLoading}
                    style={{
                      background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                      boxShadow: '0 4px 15px 0 rgba(34, 197, 94, 0.4)'
                    }}
                  >
                    Start Bot
                  </button>
                ) : (
                  <button
                    className="flex-1 px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105"
                    onClick={handleStopBot}
                    style={{
                      background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                      boxShadow: '0 4px 15px 0 rgba(239, 68, 68, 0.4)'
                    }}
                  >
                    Stop Bot
                  </button>
                )}
              </div>

              {/* Bot Status */}
              {isBotRunning && (
                <div className="flex items-center justify-center space-x-2 pt-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-500 text-sm font-medium">Bot is running</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Trading Signal Display */}
        <TradingSignal
          signal={currentSignal}
          isLoading={isLoading}
          nextCandleTime={nextCandleTime}
        />

        {/* Version Info */}
        <div className="mt-8">
          <Versions />
        </div>
      </div>
    </div>
  )
}

export default App
