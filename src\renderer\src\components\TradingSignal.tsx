import { useState, useEffect } from 'react'

interface TradeSignal {
  type: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  timestamp: number
  price: number
  suggestedDuration?: number
  reason: string
}

interface TradingSignalProps {
  signal: TradeSignal | null
  isLoading: boolean
  nextCandleTime: number | null
}

export default function TradingSignal({
  signal,
  isLoading,
  nextCandleTime
}: TradingSignalProps): React.JSX.Element {
  const [timeLeft, setTimeLeft] = useState<number>(0)

  useEffect(() => {
    if (!nextCandleTime) return

    const interval = setInterval(() => {
      const now = Date.now()
      const remaining = Math.max(0, nextCandleTime - now)
      setTimeLeft(Math.floor(remaining / 1000))
    }, 100)

    return () => clearInterval(interval)
  }, [nextCandleTime])

  const getSignalColor = (type: string): string => {
    switch (type) {
      case 'BUY':
        return 'from-green-500 to-green-600'
      case 'SELL':
        return 'from-red-500 to-red-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  const getSignalIcon = (type: string): string => {
    switch (type) {
      case 'BUY':
        return '↗'
      case 'SELL':
        return '↘'
      default:
        return '→'
    }
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* Main Signal Card */}
      <div className="relative overflow-hidden rounded-2xl bg-gray-900 shadow-2xl">
        {/* Animated Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-blue-600"></div>
          <div className="absolute inset-0 opacity-10">
            <div className="h-full w-full bg-gradient-to-br from-white/10 to-transparent"></div>
          </div>
        </div>

        <div className="relative z-10 p-8">
          {/* Loading State */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="relative">
                <div className="w-20 h-20 border-4 border-purple-600 border-opacity-30 rounded-full"></div>
                <div className="w-20 h-20 border-4 border-purple-600 border-t-transparent rounded-full animate-spin absolute top-0"></div>
              </div>
              <p className="text-gray-300 mt-4 animate-pulse">Analyzing market data...</p>
            </div>
          )}

          {/* Signal Display */}
          {!isLoading && signal && (
            <div className="space-y-6">
              {/* Signal Type and Icon */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${getSignalColor(signal.type)} flex items-center justify-center shadow-lg transform transition-transform hover:scale-110`}
                  >
                    <span className="text-3xl text-white font-bold">
                      {getSignalIcon(signal.type)}
                    </span>
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold text-white">{signal.type}</h2>
                    <p className="text-gray-400">Signal Generated</p>
                  </div>
                </div>

                {/* Confidence Meter */}
                <div className="text-right">
                  <p className="text-gray-400 text-sm">Confidence</p>
                  <p className="text-3xl font-bold text-white">{signal.confidence}%</p>
                </div>
              </div>

              {/* Price Display */}
              {signal.price && (
                <div className="bg-gray-800 bg-opacity-50 rounded-xl p-4">
                  <p className="text-gray-400 text-sm">Current Price</p>
                  <p className="text-2xl font-mono font-bold text-white">
                    {signal.price.toFixed(5)}
                  </p>
                </div>
              )}

              {/* Signal Details */}
              <div className="space-y-4">
                {/* Reason */}
                <div className="bg-gray-800 bg-opacity-50 rounded-xl p-4">
                  <p className="text-gray-400 text-sm">Signal Reason</p>
                  <p className="text-lg text-white">{signal.reason}</p>
                </div>

                {/* Suggested Duration */}
                {signal.suggestedDuration && (
                  <div className="bg-gray-800 bg-opacity-50 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">Suggested Duration</p>
                    <p className="text-xl font-bold text-white">
                      {Math.floor(signal.suggestedDuration / 60)} minutes
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* No Signal State */}
          {!isLoading && !signal && (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="w-20 h-20 rounded-2xl bg-gray-700 flex items-center justify-center">
                <span className="text-3xl text-gray-500">?</span>
              </div>
              <p className="text-gray-400 mt-4">Waiting for signal...</p>
            </div>
          )}
        </div>

        {/* Cooldown Timer */}
        {nextCandleTime && timeLeft > 0 && (
          <div className="relative z-10 bg-gray-800 px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <p className="text-gray-300">Next candle closes in</p>
              </div>
              <p className="text-2xl font-mono font-bold text-white">{formatTime(timeLeft)}</p>
            </div>

            {/* Progress Bar */}
            <div className="mt-3 w-full bg-gray-700 rounded-full h-2 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-1000 ease-linear"
                style={{ width: `${((60 - timeLeft) / 60) * 100}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
