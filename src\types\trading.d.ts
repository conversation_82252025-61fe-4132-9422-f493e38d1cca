/**
 * Represents generic market data.
 */
export interface MarketData {
  symbol: string
  price: number
  timestamp: Date
  volume?: number
  bid?: number
  ask?: number
}

/**
 * Represents a single candlestick.
 */
export interface CandleData {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

/**
 * Represents a single tick of data.
 */
export interface TickData {
  asset: string
  timestamp: number
  price: number
}

/**
 * Represents the processed market data for a symbol.
 */
export interface ProcessedMarketData {
  symbol: string
  candles: CandleData[]
  latestTick: TickData | null
  lastAnalysis: AnalysisResult | null
}

/**
 * Settings for the RSI indicator.
 */
export interface RSISettings {
  period: number
  overbought: number
  oversold: number
}

/**
 * Settings for the Bollinger Bands indicator.
 */
export interface BollingerBandsSettings {
  period: number
  stdDev: number
}

/**
 * Settings for the MACD indicator.
 */
export interface MACDSettings {
  fastPeriod: number
  slowPeriod: number
  signalPeriod: number
}

/**
 * Settings for the Stochastic Oscillator indicator.
 */
export interface StochasticOscillatorSettings {
  period: number
  kPeriod: number
  dPeriod: number
}

/**
 * Settings for the Moving Average indicator.
 */
export interface MovingAverageSettings {
  period: number
  type: 'simple' | 'exponential'
}

/**
 * The result of an RSI calculation.
 */
export interface RSIResult {
  rsi: number | null
  signal: 'BUY' | 'SELL' | 'HOLD'
  strength: 'WEAK' | 'MODERATE' | 'STRONG'
}

/**
 * The result of a Bollinger Bands calculation.
 */
export interface BollingerBandsResult {
  upper: number | null
  middle: number | null
  lower: number | null
  bandwidth: number
  squeeze: boolean
  signal: 'BUY' | 'SELL' | 'HOLD'
  strength: 'WEAK' | 'MODERATE' | 'STRONG'
}

/**
 * The result of a Moving Average calculation.
 */
export interface MovingAverageResult {
  value: number | null
  trend: 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS'
  crossoverType: 'GOLDEN_CROSS' | 'DEATH_CROSS' | 'NONE'
  signal: 'BUY' | 'SELL' | 'HOLD'
  strength: 'WEAK' | 'MODERATE' | 'STRONG'
}

/**
 * The result of a Stochastic Oscillator calculation.
 */
export interface StochasticOscillatorResult {
  k: number | null
  d: number | null
  signal: 'BUY' | 'SELL' | 'HOLD'
  strength: 'WEAK' | 'MODERATE' | 'STRONG'
}

/**
 * The combined result of all analyses.
 */
export interface AnalysisResult {
  timestamp: Date
  symbol: string
  currentPrice: number
  rsi: RSIResult | null
  bollingerBands: BollingerBandsResult | null
  movingAverage: MovingAverageResult | null
  stochasticOscillator: StochasticOscillatorResult | null
  overallSignal: 'STRONG_BUY' | 'BUY' | 'SELL' | 'HOLD' | 'STRONG_SELL'
  confidence: number
  signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG'
  bullishSignals: number
  bearishSignals: number
  neutralSignals: number
  marketCondition: 'TRENDING' | 'RANGING' | 'VOLATILE' | 'BREAKOUT'
  volatility: 'LOW' | 'MODERATE' | 'HIGH' | 'EXTREME'
  trend: 'STRONG_UPTREND' | 'UPTREND' | 'SIDEWAYS' | 'DOWNTREND' | 'STRONG_DOWNTREND'
  supportLevel: number
  resistanceLevel: number
  recommendedAction: 'BUY' | 'SELL' | 'HOLD'
  entryPrice?: number
  takeProfit?: number
  stopLoss?: number
}
