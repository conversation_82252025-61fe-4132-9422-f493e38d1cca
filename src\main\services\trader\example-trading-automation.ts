import WebSocketClient from '../websocket/WebSocketClient'
import { Logger } from '../../../shared/Logger'

const logger = new Logger('AutoTrader')

/**
 * Example of how to use the automated trading functionality
 */
export class AutomatedTrading {
  private wsClient: WebSocketClient

  constructor() {
    this.wsClient = WebSocketClient.getInstance()
  }

  /**
   * Start automated trading
   * @param asset - The asset to trade (e.g., 'USDJPY_otc')
   * @param enableAutoTrade - Whether to enable automatic trade execution
   */
  startTrading(asset: string, enableAutoTrade: boolean = false): void {
    logger.info(`Starting automated trading for ${asset}`)

    // Enable auto trading if requested
    this.wsClient.setAutoTrading(enableAutoTrade)

    // Select the asset (this will start receiving updateStream events)
    // The second parameter true means it will generate an initial signal
    this.wsClient.selectAsset(asset, false)

    // Get current trading status
    const status = this.wsClient.getTradingStatus()
    logger.info(`Trading Status: ${JSON.stringify(status, null, 2)}`)
  }

  /**
   * Stop automated trading
   */
  stopTrading(): void {
    logger.info('Stopping automated trading')
    this.wsClient.setAutoTrading(false)
  }

  /**
   * Get current trading status
   */
  getStatus(): void {
    const status = this.wsClient.getTradingStatus()
    logger.info(`Current Trading Status:
    - Auto Trade Enabled: ${status.autoTradeEnabled}
    - Current Asset: ${status.currentAsset}
    - Timeframe: ${status.timeframe}
    - Candle Count: ${status.candleCount}
    - Price History Count: ${status.priceCount}`)
  }
}

// Example usage:
const trader = new AutomatedTrading()

// Start trading USDJPY_otc with automated signals (but not executing trades yet)
trader.startTrading('USDJPY_otc', false)

// To enable actual trade execution, use:
// trader.startTrading('USDJPY_otc', true)

// Check status
setTimeout(() => {
  trader.getStatus()
}, 10000)

// Stop trading after 5 minutes
setTimeout(() => {
  trader.stopTrading()
}, 300000)
